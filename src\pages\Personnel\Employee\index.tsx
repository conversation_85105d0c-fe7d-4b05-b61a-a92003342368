import { DictionarieState } from '@/models/dictionarie';
import { getEmployeeLastCheckin } from '@/services/employee-checkins';
import { create, index, remove, update } from '@/services/employees';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { connect } from '@umijs/max';
import {
  Button,
  message,
  Popconfirm,
  Segmented,
  Space,
  Tag,
  Typography,
} from 'antd';
import dayjs from 'dayjs';
import React, { useRef, useState } from 'react';
import EditModal from './EditModal';

const { Text } = Typography;

const EmployeeManagement: React.FC<{
  dictionarie: DictionarieState;
}> = ({ dictionarie }) => {
  const actionRef = useRef<ActionType>();
  const [modalVisible, setModalVisible] = useState(false);
  const [current, setCurrent] = useState<API.Employee | undefined>(undefined);
  const [employeeCheckinMap, setEmployeeCheckinMap] = useState<
    Map<number, API.EmployeeLastCheckin>
  >(new Map());

  const handleSave = async (values: API.Employee) => {
    let response;
    console.log('values', values);
    const { id, ...info } = values;
    if (id) {
      response = await update(id, info);
    } else {
      response = await create(values);
    }

    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('操作成功');
      actionRef?.current?.reload();
      setModalVisible(false);
    }
  };

  const handleDel = async (record: API.Employee) => {
    const { id } = record;
    const response = await remove(id);
    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('删除成功');
      actionRef?.current?.reload();
    }
  };

  const columns: ProColumns<API.Employee, 'text'>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      hidden: true,
      hideInSearch: true,
    },
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      width: 80,
      fixed: 'left',
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      key: 'phone',
      width: 100,
    },
    {
      title: '头像',
      dataIndex: 'avatar',
      key: 'avatar',
      width: 100,
      hideInSearch: true,
      valueType: 'avatar',
    },
    {
      title: '职位',
      dataIndex: 'position',
      key: 'position',
      width: 100,
      hideInSearch: true,
      render: (_, record) => {
        const position = dictionarie.list
          .filter((d) => d.type === '员工职位' && !!d.status)
          .find((d) => d.code === record.position);
        return position?.name || '-';
      },
    },
    // {
    //   title: '接单等级',
    //   dataIndex: 'level',
    //   key: 'level',
    //   width: 100,
    //   hideInSearch: true,
    //   valueType: 'digit',
    //   fieldProps: {
    //     min: 1,
    //     max: 5,
    //   },
    // },
    {
      title: '工作经验(月)',
      dataIndex: 'workExp',
      key: 'workExp',
      width: 100,
      hideInSearch: true,
      valueType: 'digit',
    },
    {
      title: '服务评分',
      dataIndex: 'rating',
      key: 'rating',
      width: 100,
      hideInSearch: true,
      valueType: 'digit',
      fieldProps: {
        min: 0,
        max: 5,
      },
    },
    // {
    //   title: '钱包余额',
    //   dataIndex: 'walletBalance',
    //   key: 'walletBalance',
    //   width: 100,
    //   hideInSearch: true,
    //   valueType: 'money',
    // },
    {
      title: '所属车辆',
      dataIndex: ['vehicle', 'plateNumber'],
      key: 'vehicleId',
      width: 100,
      hideInSearch: true,
    },
    {
      title: '入职时间',
      dataIndex: 'hireDate',
      key: 'hireDate',
      width: 120,
      hideInSearch: true,
      valueType: 'date',
    },
    {
      title: '离职时间',
      dataIndex: 'resignDate',
      key: 'resignDate',
      width: 120,
      hideInSearch: true,
      valueType: 'date',
    },
    {
      title: '资格证件',
      dataIndex: 'certificates',
      key: 'certificates',
      width: 150,
      hideInSearch: true,
      render: (_, record) => {
        const certificates = record.certificates || [];
        if (certificates.length === 0) {
          return <Text type="secondary">暂无证件</Text>;
        }
        return (
          <div>
            <Tag color="blue">{certificates.length} 个证件</Tag>
            <br />
            <Text type="secondary" style={{ fontSize: 12 }}>
              {certificates
                .map((cert) => cert.name)
                .slice(0, 2)
                .join('、')}
              {certificates.length > 2 && '...'}
            </Text>
          </div>
        );
      },
    },
    {
      title: '最后打卡时间',
      dataIndex: 'lastCheckinTime',
      key: 'lastCheckinTime',
      width: 180,
      hideInSearch: true,
      render: (_, record) => {
        const checkinInfo = employeeCheckinMap.get(record.id);

        if (!checkinInfo || !checkinInfo.lastCheckInTime) {
          return (
            <div>
              <Tag color="red">从未打卡</Tag>
              <br />
              <Text type="secondary" style={{ fontSize: 12 }}>
                总计: {checkinInfo?.totalCheckInCount || 0} 次
              </Text>
            </div>
          );
        }

        const lastCheckinTime = dayjs(checkinInfo.lastCheckInTime);
        const now = dayjs();
        const daysDiff = now.diff(lastCheckinTime, 'day');

        // 超过7天显示红色警告
        if (daysDiff > 7) {
          return (
            <div>
              <Tag color="red">超期 {daysDiff} 天</Tag>
              <br />
              <Text type="secondary" style={{ fontSize: 12 }}>
                {lastCheckinTime.format('MM-DD HH:mm')}
              </Text>
              <br />
              <Text type="secondary" style={{ fontSize: 12 }}>
                总计: {checkinInfo.totalCheckInCount} 次
              </Text>
            </div>
          );
        }

        // 3-7天显示橙色提醒
        if (daysDiff > 3) {
          return (
            <div>
              <Tag color="orange">{daysDiff} 天前</Tag>
              <br />
              <Text type="secondary" style={{ fontSize: 12 }}>
                {lastCheckinTime.format('MM-DD HH:mm')}
              </Text>
              <br />
              <Text type="secondary" style={{ fontSize: 12 }}>
                总计: {checkinInfo.totalCheckInCount} 次
              </Text>
            </div>
          );
        }

        // 3天内显示绿色正常
        return (
          <div>
            <Tag color="green">
              {daysDiff === 0 ? '今天' : `${daysDiff} 天前`}
            </Tag>
            <br />
            <Text type="secondary" style={{ fontSize: 12 }}>
              {lastCheckinTime.format('MM-DD HH:mm')}
            </Text>
            <br />
            <Text type="secondary" style={{ fontSize: 12 }}>
              总计: {checkinInfo.totalCheckInCount} 次 | 今日:{' '}
              {checkinInfo.todayCheckInCount} 次
            </Text>
          </div>
        );
      },
    },
    {
      title: '在职状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      align: 'center',
      valueEnum: {
        1: '在职',
        0: '离职',
      },
      filters: true,
      render: (_, entity) => {
        return (
          <Segmented
            size="small"
            value={entity.status}
            options={[
              {
                label: '在职',
                value: 1,
              },
              {
                label: '离职',
                value: 0,
              },
            ]}
            onChange={(value) => {
              handleSave({
                ...entity,
                status: value,
              });
            }}
          />
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 130,
      valueType: 'option',
      align: 'center',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            onClick={() => {
              setCurrent(record);
              setModalVisible(true);
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确认删除？"
            onConfirm={() => {
              handleDel(record);
            }}
          >
            <Button type="link" danger>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <>
      <ProTable<API.Employee>
        actionRef={actionRef}
        rowKey="id"
        columns={columns}
        request={async (params) => {
          const { errCode, msg, data } = await index(params);
          if (errCode) {
            message.error(msg || '列表查询失败');
            return {
              data: [],
              total: 0,
            };
          }

          const employees = data?.list || [];

          // 获取每个员工的打卡信息
          const checkinMap = new Map<number, API.EmployeeLastCheckin>();
          await Promise.all(
            employees.map(async (employee) => {
              try {
                const checkinRes = await getEmployeeLastCheckin(employee.id);
                if (checkinRes.errCode === 0 && checkinRes.data) {
                  checkinMap.set(employee.id, checkinRes.data);
                }
              } catch (error) {
                console.error(`获取员工${employee.id}打卡信息失败:`, error);
              }
            }),
          );
          setEmployeeCheckinMap(checkinMap);

          return {
            data: employees,
            total: data?.total || 0,
          };
        }}
        scroll={{ x: '100%' }}
        toolBarRender={() => [
          <Button
            key="add"
            type="primary"
            onClick={() => {
              setCurrent(undefined);
              setModalVisible(true);
            }}
          >
            新增
          </Button>,
        ]}
      />
      <EditModal
        open={modalVisible}
        info={current}
        onClose={() => setModalVisible(false)}
        onSave={handleSave}
      />
    </>
  );
};

export default connect(({ dictionarie }: { dictionarie: any }) => ({
  dictionarie,
}))(EmployeeManagement);
