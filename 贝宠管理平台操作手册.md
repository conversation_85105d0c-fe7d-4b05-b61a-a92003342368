# 贝宠管理平台操作手册

## 目录
1. [系统概述](#系统概述)
2. [登录与权限](#登录与权限)
3. [功能模块详解](#功能模块详解)
4. [常见操作流程](#常见操作流程)
5. [注意事项](#注意事项)

## 系统概述

贝宠管理平台是一个专业的宠物服务管理系统，为宠物服务企业提供全方位的业务管理解决方案。系统包含车辆管理、人员管理、预约管理、服务管理、卡券管理等核心功能模块。

### 系统特点
- 基于角色的权限管理体系
- 实时数据统计与分析
- 移动端小程序配套管理
- 完整的订单生命周期管理
- 灵活的服务项目配置

## 登录与权限

### 登录流程
1. 访问管理平台登录页面
2. 输入用户名和密码
3. 系统验证身份并分配相应权限
4. 登录成功后自动跳转到主界面

### 权限体系
系统采用基于角色的权限管理（RBAC），包含以下层级：
- **用户（Users）**：系统操作人员
- **角色（Roles）**：权限的集合
- **权限点（Permissions）**：具体的功能权限
- **功能模块（Features）**：系统功能的分组

### 权限代码说明
- `010000`：车辆管理
- `020100`：员工信息管理
- `020200`：用户数据管理
- `030000`：预约管理
- `050100`：轮播图管理
- `050300`：照片墙管理
- `050400`：活动页管理
- `060100`：投诉建议管理
- `060200`：员工建议管理
- `070000`：员工出车拍照
- `080000`：员工操作统计
- `090100`：服务品牌管理
- `090200`：服务项目管理
- `100100`：权益卡管理
- `100200`：代金券管理
- `980100`：数据字典管理
- `980200`：区域管理
- `990100`：用户管理
- `990200`：角色管理
- `990300`：权限点维护
- `990400`：系统功能维护
- `990500`：数据一致性维护

## 功能模块详解

### 1. 车辆管理
**功能描述**：管理服务车辆的基本信息、状态和调度

**主要操作**：
- 车辆信息录入与编辑
- 车辆状态管理（可用/维修/停用）
- 车辆位置跟踪
- 车辆调度安排

**操作步骤**：
1. 进入"车辆管理"模块
2. 点击"新增"按钮添加车辆
3. 填写车辆基本信息（车牌号、型号、司机等）
4. 设置车辆状态和服务区域
5. 保存车辆信息

### 2. 人员管理

#### 2.1 员工信息管理
**功能描述**：管理服务人员的基本信息、技能和工作状态

**主要操作**：
- 员工档案管理
- 技能认证管理
- 工作状态跟踪
- 绩效考核记录

#### 2.2 用户数据管理
**功能描述**：管理客户信息和数据统计分析

**主要操作**：
- 客户信息查看与编辑
- 用户行为数据分析
- 会员等级管理
- 客户服务记录

### 3. 预约管理
**功能描述**：处理客户预约订单的全生命周期管理

**主要功能**：
- 订单状态看板
- 订单详情管理
- 派单与改派
- 服务完成确认
- 退款处理

**订单状态流程**：
```
待接单 → 待服务 → 已出发 → 服务中 → 已完成
         ↓
      已取消/已退款
```

**常用操作**：
- **派单**：将待接单的订单分配给具体员工
- **改派**：重新分配已派发的订单
- **修改地址**：更新服务地址信息
- **录入投诉**：记录客户投诉信息
- **审核退款**：处理客户退款申请

### 4. 小程序模块管理

#### 4.1 轮播图管理
**功能描述**：管理小程序首页轮播图内容

**操作步骤**：
1. 上传轮播图片
2. 设置跳转链接
3. 配置显示顺序
4. 设置生效时间

#### 4.2 照片墙管理
**功能描述**：管理展示的服务案例照片

#### 4.3 活动页管理
**功能描述**：管理营销活动页面内容

### 5. 投诉建议管理

#### 5.1 投诉建议管理
**功能描述**：处理客户投诉和建议

#### 5.2 员工建议
**功能描述**：收集和处理员工内部建议

### 6. 服务管理

#### 6.1 服务品牌管理
**功能描述**：管理服务品牌分类

#### 6.2 服务项目管理
**功能描述**：管理具体的服务项目和定价

**主要操作**：
- 服务项目创建与编辑
- 价格设置与调整
- 服务时长配置
- 服务描述管理

### 7. 卡券管理

#### 7.1 权益卡管理
**功能描述**：管理会员权益卡的发放和使用

#### 7.2 代金券管理
**功能描述**：管理优惠券的创建、发放和核销

**主要操作**：
- 券种创建与配置
- 发放规则设置
- 使用状态跟踪
- 核销记录查询

### 8. 基础数据管理

#### 8.1 数据字典管理
**功能描述**：管理系统中的基础数据配置

#### 8.2 区域管理
**功能描述**：管理服务区域划分

### 9. 系统管理

#### 9.1 用户管理
**功能描述**：管理系统操作用户账号

**主要操作**：
- 用户账号创建
- 密码重置
- 账号状态管理
- 角色分配

#### 9.2 角色管理
**功能描述**：管理用户角色和权限分配

#### 9.3 权限点维护
**功能描述**：维护系统功能权限点

#### 9.4 系统功能维护
**功能描述**：管理系统功能模块

#### 9.5 数据一致性维护
**功能描述**：检查和修复数据一致性问题

## 常见操作流程

### 订单处理流程
1. **接收订单**：系统自动接收客户预约
2. **订单分配**：根据地区和服务类型分配给合适员工
3. **服务执行**：员工按时到达提供服务
4. **服务完成**：确认服务完成并收集客户反馈
5. **订单结算**：处理费用结算和开票

### 投诉处理流程
1. **投诉接收**：记录客户投诉信息
2. **问题调查**：核实投诉内容和相关情况
3. **处理方案**：制定解决方案
4. **执行处理**：实施解决措施
5. **跟踪反馈**：确认客户满意度

### 员工管理流程
1. **员工入职**：录入员工基本信息
2. **技能认证**：完成相关技能培训和认证
3. **工作分配**：根据技能和区域分配工作
4. **绩效考核**：定期评估工作表现
5. **奖惩管理**：根据表现给予相应奖惩

## 注意事项

### 操作安全
- 定期修改登录密码
- 不要在公共场所使用系统
- 及时退出登录
- 谨慎使用删除功能

### 数据管理
- 重要操作前请确认数据准确性
- 定期备份重要数据
- 遵守客户隐私保护规定
- 及时更新过期信息

### 系统使用
- 建议使用Chrome或Edge浏览器
- 保持网络连接稳定
- 遇到问题及时联系技术支持
- 关注系统更新通知

### 权限管理
- 严格按照权限范围操作
- 不要共享账号密码
- 发现权限异常及时报告
- 离职人员及时注销账号

---

**技术支持**：如遇到系统问题，请联系技术支持团队
**更新日期**：2025年7月
**版本**：v1.0
